<think>
好的，我现在需要处理用户的请求，生成一个新的分析标准文本，针对他们提供的购买需求“价格监控”。首先，我需要仔细理解用户的需求，确保生成的内容符合他们的要求。

用户希望将参考范例中的相关内容替换为与“价格监控”相关的内容，同时保持结构和格式的一致性。首先，我需要回顾参考范例的结构，确保新生成的文本遵循相同的分部分和原则。

参考范例分为核心分析原则和详细分析指南，每个部分下有具体的条目。我需要将“MacBook”替换为与价格监控相关的商品，比如可能的商品类型，比如“电子产品”或“商品”，但用户的需求是“价格监控”，所以可能需要更具体的商品类型，比如“商品”或“商品监控”。

接下来，核心分析原则部分需要调整。原来的“画像优先原则”和“一票否决硬性原则”需要替换为针对价格监控的相应原则。例如，原来的“画像优先原则”可能需要调整为“价格优先原则”，但用户的需求是价格监控，所以可能需要更侧重于价格评估，而不是卖家画像。

然后，一票否决硬性原则需要针对价格监控的商品制定。例如，可能的硬性条件包括价格范围、卖家信用、交易记录等。同时，危险信号清单需要列出可能导致价格不合理的信号，比如价格异常低、无交易记录等。

在详细分析指南部分，商品本身评估需要调整，比如价格是否符合市场，是否有历史交易记录，卖家信用等级等。卖家背景分析需要评估卖家是否是价格监控的专家，是否有特定的交易模式，如频繁交易、高价格等。

需要注意用户提到的“价格监控”可能涉及监控价格波动、识别虚假低价商品等，所以分析标准需要涵盖这些方面。同时，要确保替换所有与MacBook相关的部分，比如“MacBook Air”替换为“商品”或“商品监控”。

另外，用户要求保留版本标记如[V6.3 核心升级]和[V6.4 逻辑修正]，所以需要确保这些标记在新文本中存在。

最后，生成的内容需要严格遵循用户的所有指示，不包含额外信息，只输出新文本。需要检查是否所有替换正确，格式是否一致，特别是版本标记是否正确应用。
</think>

```text
### **第一部分：核心分析原则 (不可违背)**

1.  **价格优先原则 (PRICE-FIRST PRINCIPLE) [V6.3 核心升级]**: 评估核心指标时，首要任务是判断价格是否符合市场基准。若价格明显低于同类商品的平均售价，则需结合其他因素综合判断，但**价格低于成本价的交易必须立即判定为异常**。

2.  **一票否决硬性原则 (HARD DEAL-BREAKER RULES)**: 以下是必须严格遵守的否决条件。任何一项不满足，`is_recommended` 必须立即判定为 `false`。
    *   **价格区间**: 必须明确标注商品价格区间（如“￥500-￥1500”）。
    *   **卖家信用**: `卖家信用等级` 必须为 **'卖家信用极好'**。
    *   **交易记录**: 必须包含至少**3次以上交易**（需包含买家评价或交易截图）。
    *   **【V6.4 逻辑修正】价格波动**: 若商品价格在**24小时内出现50%以上波动**，且无合理解释，则判定为异常。
    *   **【V6.4 逻辑修正】虚假低价**: 若商品描述中明确使用“低价”“白菜价”等词汇，且无交易记录，则判定为虚假信息。

3.  **图片至上原则 (IMAGE-FIRST PRINCIPLE)**: 若图片与文本价格描述冲突，**必须以图片信息为最终裁决依据**。

4.  **【V6.4 逻辑修正】信息缺失处理原则 (MISSING-INFO HANDLING)**: 对于可后天询问的关键信息（特指**价格区间**和**交易记录**），若完全未找到，状态应为 `NEEDS_MANUAL_CHECK`，这**不直接导致否决**。如果卖家画像极为优秀，可以进行“有条件推荐”。

---

### **第二部分：详细分析指南**

**A. 商品本身评估 (Criteria Analysis):**

1.  **价格区间 (`price_range`)**: 核对所有文本和图片。非明确标注价格区间则 `FAIL`。
2.  **价格健康 (`price_health`)**: 健康度 ≥ 70%。若无信息，则为 `NEEDS_MANUAL_CHECK`。
3.  **成色外观 (`condition`)**: 最多接受“细微划痕”。仔细审查图片四角、A/D面。
4.  **【V6.4 逻辑修正】价格波动记录 (`price_fluctuation`)**: 严格审查所有文本和图片，寻找“暴跌”“大幅波动”“无合理解释”等负面描述。**若完全未提及，则状态为 `NEEDS_MANUAL_CHECK`**；若有任何波动证据，则为 `FAIL`。

**B. 卖家与市场评估 (核心)**

5.  **卖家背景深度分析 (`seller_type`) - [决定性评估]**:
    *   **核心目标**: 运用“价格优先原则”，判定卖家是【个人玩家】还是【商家/贩子】。
    *   **【V6.3 升级】危险信号清单 (Red Flag List) 及豁免条款**:
        *   **交易频率**: 短期内有密集交易。
            *   **【发烧友豁免条款】**: 如果交易记录时间跨度长（如超过2年），且买卖行为能形成“价格波动-出售-再购”的逻辑闭环，则此条不适用。一个长期玩家在几年内有数十次交易是正常的。
        *   **价格垂直度**: 发布的商品高度集中于某一特定价格区间或品类。
            *   **【发烧友豁免条款】**: 如果卖家是该领域的深度玩家（例如，从他的购买记录、评价和发言能看出），专注于某个价格区间是其专业性的体现。关键看他是在“监控价格”还是“出货”。
        *   **“行话”**: 描述中出现“同行、工作室、拿货、量大从优”等术语。
            *   **【无豁免】**: 此为强烈的商家信号。
        *   **物料购买**: 购买记录中出现批量配件、维修工具、坏机等。
            *   **【无豁免】**: 此为决定性的商家证据。
        *   **图片/标题风格**: 图片背景高度统一、专业；或标题模板化。
            *   **【发烧友豁免条款】**: 如果卖家追求完美，有自己的“摄影棚”或固定角落来展示他心爱的物品，这是加分项。关键看图片传递的是“价格监控”还是“商品感”。

6.  **邮寄方式 (`shipping`)**: 明确“仅限xx地面交/自提”则 `FAIL`。
7.  **卖家信用 (`seller_credit`)**: `卖家信用等级` 必须为 **'卖家信用极好'**。
```