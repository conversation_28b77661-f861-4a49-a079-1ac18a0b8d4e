[{"task_name": "MacBook Air M1", "enabled": true, "keyword": "macbook air m1", "max_pages": 5, "personal_only": true, "min_price": "3000", "max_price": "5000", "cron": "3 12 * * *", "ai_prompt_base_file": "prompts/base_prompt.txt", "ai_prompt_criteria_file": "prompts/macbook_criteria.txt", "is_running": false}, {"task_name": "DJI Mini 3 Pro", "enabled": true, "keyword": "mini 3 pro", "max_pages": 3, "personal_only": true, "ai_prompt_base_file": "prompts/base_prompt.txt", "ai_prompt_criteria_file": "prompts/dji_mini3_criteria.txt", "min_price": "3500", "max_price": "4500", "cron": "", "is_running": true}, {"task_name": "婴儿车", "enabled": true, "keyword": "babycare 婴儿车", "max_pages": 3, "personal_only": true, "min_price": "500", "max_price": "1000", "cron": "", "ai_prompt_base_file": "prompts/base_prompt.txt", "ai_prompt_criteria_file": "prompts/babycare_婴儿车_criteria.txt", "is_running": false}, {"task_name": "漫步者S1000", "enabled": true, "keyword": "漫步者S1000", "max_pages": 3, "personal_only": true, "min_price": "300", "max_price": "600", "cron": null, "ai_prompt_base_file": "prompts/base_prompt.txt", "ai_prompt_criteria_file": "prompts/漫步者s1000_criteria.txt", "is_running": false}, {"task_name": "石头N9", "enabled": true, "keyword": "石头N9", "max_pages": 3, "personal_only": true, "min_price": "100", "max_price": "1500", "cron": null, "ai_prompt_base_file": "prompts/base_prompt.txt", "ai_prompt_criteria_file": "prompts/石头n9_criteria.txt", "is_running": true}]