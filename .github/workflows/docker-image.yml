name: Docker Image CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:
    runs-on: ubuntu-latest

    # Add this permissions block
    permissions:
      contents: read
      packages: write

    steps:
    - uses: actions/checkout@v4
    
    - name: Build the Docker image
      run: docker build . --file Dockerfile --tag ai-goofish:latest
      
    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Push Docker image
      run: |
        docker tag ai-goofish:latest ghcr.io/${{ github.repository_owner }}/ai-goofish:latest
        docker push ghcr.io/${{ github.repository_owner }}/ai-goofish:latest
